<div>
    <div class="page-header">
        <div class="page-block">
           <div class="row align-items-center">
              <div class="col-md-12">
                 <div class="page-header-title">
                    <h2 class="mb-0">Downline</h2>
                 </div>
              </div>
           </div>
        </div>
     </div>


     <div class="row">

        <div class="col-md-12 col-xxl-4">
           <div class="card statistics-card-1">
              <div class="card-body">
                 <img src="{{asset('assets/images/widget/img-status-3.svg')}}" alt="img" class="img-fluid img-bg">
                 <div class="d-flex align-items-center">
                    <div class="text-white avtar bg-brand-color-1 me-3"><i class="ph-duotone ph-users-four f-26"></i></div>
                    <div>
                       <p class="mb-0 text-muted">Total Referrals</p>
                       <div class="d-flex align-items-end">
                          <h2 class="mb-0 f-w-500">{{ number_format($this->totalReferrals) }}</h2>
                       </div>
                    </div>
                 </div>
              </div>
           </div>
        </div>
        <div class="col-md-6 col-xxl-4">
           <div class="card statistics-card-1">
              <div class="card-body">
                 <img src="{{asset('assets/images/widget/img-status-3.svg')}}" alt="img" class="img-fluid img-bg">
                 <div class="d-flex align-items-center">
                    <div class="text-white avtar bg-brand-color-2 me-3"><i class="ph-duotone ph-calendar f-26"></i></div>
                    <div>
                       <p class="mb-0 text-muted">Previous Month Referrals</p>
                       <div class="d-flex align-items-end">
                          <h2 class="mb-0 f-w-500">{{ number_format($this->previousMonthReferrals) }}</h2>
                          <small class="text-muted ms-2">{{ \Carbon\Carbon::now()->subMonth()->format('M Y') }}</small>
                       </div>
                    </div>
                 </div>
              </div>
           </div>
        </div>
        <div class="col-md-6 col-xxl-4">
           <div class="card statistics-card-1">
              <div class="card-body">
                 <img src="{{asset('assets/images/widget/img-status-3.svg')}}" alt="img" class="img-fluid img-bg">
                 <div class="d-flex align-items-center">
                    <div class="text-white avtar bg-brand-color-3 me-3"><i class="ph-duotone ph-trend-up f-26"></i></div>
                    <div>
                       <p class="mb-0 text-muted">Current Month Referrals</p>
                       <div class="d-flex align-items-end">
                          <h2 class="mb-0 f-w-500">{{ number_format($this->currentMonthReferrals) }}</h2>
                          <small class="text-muted ms-2">{{ \Carbon\Carbon::now()->format('M Y') }}</small>
                       </div>
                    </div>
                 </div>
              </div>
           </div>
        </div>




        <div class="col-sm-12">

           <div class="mb-3 row">
              <div class="col-sm-10">
                 <label class="form-label">Search Referrals</label>
                 <div class="input-group">
                    <span class="input-group-text"><i class="ph-duotone ph-magnifying-glass"></i></span>
                    <input class="form-control" type="text" placeholder="Search by customer name or email..." wire:model.live.debounce.300ms="search" />
                 </div>
              </div>
              <div class="col-sm-2 d-flex align-items-end">
                 @if($search)
                    <button class="btn btn-outline-secondary w-100" wire:click="clearSearch">
                       <i class="ph-duotone ph-x me-1"></i>Clear
                    </button>
                 @endif
              </div>
           </div>

           <div class="card">
              <div class="card-body table-border-style">
                 <div class="table-responsive">
                    <table class="table table-hover">
                       <thead>
                          <tr>
                             <th>#</th>
                             <th>Name</th>
                             <th>Email</th>
                             <th class="text-end">Register At</th>
                          </tr>
                       </thead>
                       <tbody>
                          @forelse($downlineData as $index => $customer)
                          <tr>
                             <td>{{ $index + 1 }}</td>
                             <td>{{ $customer->nama }}</td>
                             <td>{{ $customer->email }}</td>
                             <td class="text-end">
                                <span class="badge text-bg-success">
                                   {{ \Carbon\Carbon::parse($customer->tarikh)->format('M d, Y') }}
                                </span>
                             </td>
                          </tr>
                          @empty
                          <tr>
                             <td colspan="4" class="py-4 text-center">
                                <div class="d-flex flex-column align-items-center">
                                   <i class="ph-duotone ph-users-four" style="font-size: 3rem; color: #6c757d;"></i>
                                   <p class="mt-2 mb-0 text-muted">No referrals found</p>
                                   <small class="text-muted">Start sharing your referral code to get customers</small>
                                </div>
                             </td>
                          </tr>
                          @endforelse
                       </tbody>
                    </table>
                 </div>
              </div>
           </div>
        </div>

     </div>
</div>

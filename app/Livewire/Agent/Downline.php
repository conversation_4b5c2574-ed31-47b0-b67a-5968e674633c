<?php

namespace App\Livewire\Agent;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class Downline extends Component
{
    public $search = '';
    public $downlineData = [];
    public $totalReferrals = 0;
    public $previousMonthReferrals = 0;
    public $currentMonthReferrals = 0;


    public function render()
    {

        $this->downlineData = DB::table('senarai_pelanggan')
            ->join('acs_users', 'acs_users.id', '=', 'senarai_pelanggan.introducer_id')
            ->select('senarai_pelanggan.nama', 'senarai_pelanggan.email', 'senarai_pelanggan.tarikh')
            ->where('senarai_pelanggan.acs_users_id', Auth::user()->id)
            ->get();


        return view('livewire.agent.downline', [
            'downlineData' => $this->downlineData,
        ]);
    }
}
